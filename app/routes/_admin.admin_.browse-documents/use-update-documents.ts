import type { UpdateRecordType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { UPDATE_RECORD } from './graphql'
import { toast } from 'sonner'
import { parseGraphqlError } from '~/lib/parse-graphql-error'

export default function useUpdateDocuments() {
  const queryClient = useQueryClient()
  const updateBaptismaRecord = useMutation({
    mutationFn: async (data: UpdateRecordType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_RECORD,
        variables: {
          baptisma_record: data.baptisma_record,
          document: data.document,
        },
      })
    },
    onSuccess: () => {
      toast.success('Record updated')
      queryClient.invalidateQueries({
        queryKey: ['get-baptisma'],
      })
    },
    onError: (err) => {
      toast.error(parseGraphqlError(err))
    },
  })

  const updateInneihRecord = useMutation({
    mutationFn: async (data: UpdateRecordType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_RECORD,
        variables: {
          inneih_record: data.inneih_record,
          document: data.document,
        },
      })
    },
    onError: (err) => {
      toast.error(parseGraphqlError(err))
    },
    onSuccess: () => {
      toast.success('Record updated')
      queryClient.invalidateQueries({
        queryKey: ['get-inneih'],
      })
    },
  })

  const updateOthersRecord = useMutation({
    mutationFn: async (data: UpdateRecordType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_RECORD,
        variables: {
          document: data.document,
        },
      })
    },
    onSuccess: () => {
      toast.success('Record updated')
      queryClient.invalidateQueries({
        queryKey: ['get-others'],
      })
    },
    onError: (err) => {
      toast.error(parseGraphqlError(err))
    },
  })

  return { updateBaptismaRecord, updateInneihRecord, updateOthersRecord }
}
